import prisma from '@/config/prismaClient';
import { UserType, ActivityType } from '@prisma/client';

interface GetActivityLogsParams {
  page: number;
  limit: number;
  userType?: string;
  activityType?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
}

interface GetUserActivityLogsParams {
  userId: string;
  page: number;
  limit: number;
  activityType?: string;
}

interface GetActivityLogStatsParams {
  startDate?: string;
  endDate?: string;
}

export const getActivityLogs = async (params: GetActivityLogsParams) => {
  const { page, limit, userType, activityType, search, startDate, endDate } = params;
  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};

  if (userType && Object.values(UserType).includes(userType as UserType)) {
    where.userType = userType as UserType;
  }

  if (activityType && Object.values(ActivityType).includes(activityType as ActivityType)) {
    where.activityType = activityType as ActivityType;
  }

  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) {
      where.createdAt.gte = new Date(startDate);
    }
    if (endDate) {
      where.createdAt.lte = new Date(endDate);
    }
  }

  // Get activity logs with user details
  const [logs, total] = await Promise.all([
    prisma.userActivityLog.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.userActivityLog.count({ where }),
  ]);

  // Enrich logs with user details
  const enrichedLogs = await Promise.all(
    logs.map(async (log) => {
      let userDetails = null;
      
      if (log.userType === UserType.STUDENT) {
        userDetails = await prisma.student.findUnique({
          where: { id: log.userId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contact: true,
            profilePhoto: true,
          },
        });
      } else if (log.userType === UserType.CLASS) {
        userDetails = await prisma.classes.findUnique({
          where: { id: log.userId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contactNo: true,
            className: true,
          },
        });
      }

      return {
        ...log,
        userDetails,
      };
    })
  );

  // Filter by search if provided
  let filteredLogs = enrichedLogs;
  if (search) {
    const searchLower = search.toLowerCase();
    filteredLogs = enrichedLogs.filter((log) => {
      const userDetails = log.userDetails;
      if (!userDetails) return false;
      
      const fullName = `${userDetails.firstName} ${userDetails.lastName}`.toLowerCase();
      const email = userDetails.email?.toLowerCase() || '';

      // Handle contact field based on user type
      let contact = '';
      if (log.userType === UserType.STUDENT && 'contact' in userDetails) {
        contact = userDetails.contact || '';
      } else if (log.userType === UserType.CLASS && 'contactNo' in userDetails) {
        contact = userDetails.contactNo || '';
      }

      return fullName.includes(searchLower) ||
             email.includes(searchLower) ||
             contact.toLowerCase().includes(searchLower);
    });
  }

  return {
    logs: search ? filteredLogs : enrichedLogs,
    pagination: {
      page,
      limit,
      total: search ? filteredLogs.length : total,
      totalPages: search ? Math.ceil(filteredLogs.length / limit) : Math.ceil(total / limit),
    },
  };
};

export const getUserActivityLogs = async (params: GetUserActivityLogsParams) => {
  const { userId, page, limit, activityType } = params;
  const skip = (page - 1) * limit;

  const where: any = { userId };

  if (activityType && Object.values(ActivityType).includes(activityType as ActivityType)) {
    where.activityType = activityType as ActivityType;
  }

  const [logs, total] = await Promise.all([
    prisma.userActivityLog.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.userActivityLog.count({ where }),
  ]);

  return {
    logs,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };
};

export const getActivityLogStatistics = async (params: GetActivityLogStatsParams) => {
  const { startDate, endDate } = params;

  const where: any = {};
  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) {
      where.createdAt.gte = new Date(startDate);
    }
    if (endDate) {
      where.createdAt.lte = new Date(endDate);
    }
  }

  // Get overall statistics
  const [
    totalLogs,
    loginCount,
    logoutCount,
    registrationCount,
    studentLogs,
    classLogs,
  ] = await Promise.all([
    prisma.userActivityLog.count({ where }),
    prisma.userActivityLog.count({ where: { ...where, activityType: ActivityType.LOGIN } }),
    prisma.userActivityLog.count({ where: { ...where, activityType: ActivityType.LOGOUT } }),
    prisma.userActivityLog.count({ where: { ...where, activityType: ActivityType.REGISTRATION } }),
    prisma.userActivityLog.count({ where: { ...where, userType: UserType.STUDENT } }),
    prisma.userActivityLog.count({ where: { ...where, userType: UserType.CLASS } }),
  ]);

  // Get daily activity for the last 7 days
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  const dailyActivity = await prisma.userActivityLog.groupBy({
    by: ['activityType'],
    where: {
      ...where,
      createdAt: { gte: sevenDaysAgo },
    },
    _count: true,
  });

  return {
    overview: {
      totalLogs,
      loginCount,
      logoutCount,
      registrationCount,
      studentLogs,
      classLogs,
    },
    dailyActivity,
  };
};
