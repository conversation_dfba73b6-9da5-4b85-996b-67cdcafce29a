import { Router } from 'express';
import { 
  getAllActivityLogs, 
  getActivityLogsByUser, 
  getActivityLogStats 
} from '../controllers/activityLogController';
import { authMiddleware } from '@/middlewares/adminAuth';

const activityLogRouter = Router();

// All routes require admin authentication
activityLogRouter.use(authMiddleware);

// Get all activity logs with pagination and filters
activityLogRouter.get('/', getAllActivityLogs);

// Get activity logs for a specific user
activityLogRouter.get('/user/:userId', getActivityLogsByUser);

// Get activity log statistics
activityLogRouter.get('/stats', getActivityLogStats);

export default activityLogRouter;
