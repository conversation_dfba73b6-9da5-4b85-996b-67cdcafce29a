import { Router } from 'express';
import { 
  getAllActivityLogs, 
  getActivityLogsByUser, 
  getActivityLogStats 
} from '../controllers/activityLogController';
import { authMiddleware } from '@/middlewares/adminAuth';

const activityLogRouter = Router();

activityLogRouter.use(authMiddleware);

activityLogRouter.get('/', getAllActivityLogs);

activityLogRouter.get('/user/:userId', getActivityLogsByUser);

activityLogRouter.get('/stats', getActivityLogStats);

export default activityLogRouter;
