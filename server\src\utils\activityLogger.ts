import { Request } from 'express';
import prisma from '@/config/prismaClient';
import { UserType, ActivityType } from '@prisma/client';

interface ActivityLogData {
  userId: string;
  userType: UserType;
  activityType: ActivityType;
  req?: Request;
  additionalInfo?: {
    deviceInfo?: string;
    location?: string;
  };
}

export const logUserActivity = async ({
  userId,
  userType,
  activityType,
  req,
  additionalInfo
}: ActivityLogData): Promise<void> => {
  try {
    const ipAddress = req ? getClientIP(req) : null;
    const userAgent = req ? req.headers['user-agent'] : null;

    await prisma.userActivityLog.create({
      data: {
        userId,
        userType,
        activityType,
        ipAddress,
        userAgent,
        deviceInfo: additionalInfo?.deviceInfo || null,
        location: additionalInfo?.location || null,
      },
    });

    console.log(`Activity logged: ${userType} ${userId} - ${activityType}`);
  } catch (error) {
    console.error('Failed to log user activity:', error);
    // Don't throw error to avoid breaking the main flow
  }
};

// Helper function to get client IP address
const getClientIP = (req: Request): string => {
  const forwarded = req.headers['x-forwarded-for'];
  const realIP = req.headers['x-real-ip'];
  
  if (forwarded) {
    return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0];
  }
  
  if (realIP) {
    return Array.isArray(realIP) ? realIP[0] : realIP;
  }
  
  return req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
};

// Helper functions for specific activity types
export const logLogin = (userId: string, userType: UserType, req: Request) => {
  return logUserActivity({
    userId,
    userType,
    activityType: ActivityType.LOGIN,
    req,
  });
};

export const logLogout = (userId: string, userType: UserType, req?: Request) => {
  return logUserActivity({
    userId,
    userType,
    activityType: ActivityType.LOGOUT,
    req,
  });
};

export const logRegistration = (userId: string, userType: UserType, req: Request) => {
  return logUserActivity({
    userId,
    userType,
    activityType: ActivityType.REGISTRATION,
    req,
  });
};
