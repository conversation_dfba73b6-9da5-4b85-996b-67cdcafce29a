import prisma from '@/config/prismaClient';
import { UserType } from '@prisma/client';

export const ACTIVITY_TYPES = {
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  REGISTRATION: 'REGISTRATION',
} as const;

interface ActivityLogData {
  userId: string;
  userType: UserType;
  activityType: string;
}

export const logUserActivity = async ({
  userId,
  userType,
  activityType,
}: ActivityLogData): Promise<void> => {
  try {
    await prisma.userActivityLog.create({
      data: {
        userId,
        userType,
        activityType,
      }
    });

    console.log(`Activity logged: ${userType} ${userId} - ${activityType}`);
  } catch (error) {
    console.error('Failed to log user activity:', error);
  }
};

export const logLogin = (userId: string, userType: UserType) => {
  return logUserActivity({
    userId,
    userType,
    activityType: ACTIVITY_TYPES.LOGIN,
  });
};

export const logLogout = (userId: string, userType: UserType) => {
  return logUserActivity({
    userId,
    userType,
    activityType: ACTIVITY_TYPES.LOGOUT,
  });
};

export const logRegistration = (userId: string, userType: UserType) => {
  return logUserActivity({
    userId,
    userType,
    activityType: ACTIVITY_TYPES.REGISTRATION,
  });
};
