import prisma from '@/config/prismaClient';
import { UserType, ActivityType } from '@prisma/client';

interface ActivityLogData {
  userId: string;
  userType: UserType;
  activityType: ActivityType;
}

export const logUserActivity = async ({
  userId,
  userType,
  activityType,
}: ActivityLogData): Promise<void> => {
  try {
    await prisma.userActivityLog.create({
      data: {
        userId,
        userType,
        activityType,
      },
    });

    console.log(`Activity logged: ${userType} ${userId} - ${activityType}`);
  } catch (error) {
    console.error('Failed to log user activity:', error);
  }
};

export const logLogin = (userId: string, userType: UserType) => {
  return logUserActivity({
    userId,
    userType,
    activityType: ActivityType.LOGIN,
  });
};

export const logLogout = (userId: string, userType: UserType) => {
  return logUserActivity({
    userId,
    userType,
    activityType: ActivityType.LOGOUT,
  });
};

export const logRegistration = (userId: string, userType: UserType) => {
  return logUserActivity({
    userId,
    userType,
    activityType: ActivityType.REGISTRATION,
  });
};
