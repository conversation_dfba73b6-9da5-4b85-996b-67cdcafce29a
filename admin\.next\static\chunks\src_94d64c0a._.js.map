{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/table.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<'table'>) {\r\n  return (\r\n    <div data-slot=\"table-container\" className=\"relative w-full overflow-x-auto\">\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn('w-full caption-bottom text-sm', className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<'thead'>) {\r\n  return <thead data-slot=\"table-header\" className={cn('[&_tr]:border-b', className)} {...props} />;\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<'tbody'>) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn('[&_tr:last-child]:border-0', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<'tfoot'>) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn('bg-muted/50 border-t font-medium [&>tr]:last:border-b-0', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<'tr'>) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        'hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<'th'>) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        'text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<'td'>) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        'p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCaption({ className, ...props }: React.ComponentProps<'caption'>) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn('text-muted-foreground mt-4 text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption };\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QAAI,aAAU;QAAkB,WAAU;kBACzC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAVS;AAYT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBAAO,6LAAC;QAAM,aAAU;QAAe,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAC/F;MAFS;AAIT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2DAA2D;QACxE,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAwC;IAC5E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/dataTable.tsx"], "sourcesContent": ["import {\r\n  useReactTable,\r\n  getCoreRowModel,\r\n  ColumnDef,\r\n  flexRender,\r\n} from \"@tanstack/react-table\";\r\n\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\n\r\ninterface DataTableProps<T> {\r\n  columns: ColumnDef<T>[];\r\n  data: T[];\r\n  isLoading?: boolean;\r\n  getRowClassName?: (row: T, index: number) => string;\r\n}\r\n\r\nexport function DataTable<T>({\r\n  columns,\r\n  data,\r\n  isLoading,\r\n  getRowClassName,\r\n}: DataTableProps<T>) {\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      {isLoading ? (\r\n        <div className=\"flex justify-center items-center h-64\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader className=\"sticky top-0 bg-muted z-10\">\r\n              {table.getHeaderGroups().map((headerGroup) => (\r\n                <TableRow key={headerGroup.id}>\r\n                  {headerGroup.headers.map((header) => (\r\n                    <TableHead key={header.id}>\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext()\r\n                          )}\r\n                    </TableHead>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableHeader>\r\n            <TableBody>\r\n              {table.getRowModel().rows?.length ? (\r\n                table.getRowModel().rows.map((row, index) => {\r\n                  const customClassName = getRowClassName\r\n                    ? getRowClassName(row.original, index)\r\n                    : \"\";\r\n                  return (\r\n                    <TableRow\r\n                      key={row.id}\r\n                      className={`hover:bg-gray-50 ${customClassName}`}\r\n                    >\r\n                      {row.getVisibleCells().map((cell) => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  );\r\n                })\r\n              ) : (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={columns.length}\r\n                    className=\"text-center py-4\"\r\n                  >\r\n                    No data found.\r\n                  </TableCell>\r\n                </TableRow>\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAOA;;;;;AAgBO,SAAS,UAAa,EAC3B,OAAO,EACP,IAAI,EACJ,SAAS,EACT,eAAe,EACG;;IAClB,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;IACjC;IAEA,qBACE,6LAAC;kBACE,0BACC,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;iCAGjB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kCACJ,6LAAC,oIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,6LAAC,oIAAA,CAAA,WAAQ;0CACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,6LAAC,oIAAA,CAAA,YAAS;kDACP,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;uCALT,OAAO,EAAE;;;;;+BAFd,YAAY,EAAE;;;;;;;;;;kCAcjC,6LAAC,oIAAA,CAAA,YAAS;kCACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK;4BACjC,MAAM,kBAAkB,kBACpB,gBAAgB,IAAI,QAAQ,EAAE,SAC9B;4BACJ,qBACE,6LAAC,oIAAA,CAAA,WAAQ;gCAEP,WAAW,CAAC,iBAAiB,EAAE,iBAAiB;0CAE/C,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,6LAAC,oIAAA,CAAA,YAAS;kDACP,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;uCAHH,KAAK,EAAE;;;;;+BAJpB,IAAI,EAAE;;;;;wBAajB,mBAEA,6LAAC,oIAAA,CAAA,WAAQ;sCACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gCACR,SAAS,QAAQ,MAAM;gCACvB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA3EgB;;QAMA,yLAAA,CAAA,gBAAa;;;KANb", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input mt-2 data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <div className=\"flex-1 text-left\">\r\n        {children}\r\n      </div>\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50 ml-2\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-2.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-2.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oyBACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MA1BS;AA4BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAbS;AAeT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAbS", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/services/activityLogApi.ts"], "sourcesContent": ["import axiosInstance from '@/lib/axios';\nimport { ActivityLogResponse, ActivityLogStats, GetActivityLogsParams } from '@/lib/types';\n\nexport const getActivityLogs = async (params: GetActivityLogsParams = {}): Promise<ActivityLogResponse> => {\n  const response = await axiosInstance.get('/activity-logs', { params });\n  return response.data.data;\n};\n\nexport const getUserActivityLogs = async (\n  userId: string, \n  params: { page?: number; limit?: number; activityType?: string } = {}\n): Promise<ActivityLogResponse> => {\n  const response = await axiosInstance.get(`/activity-logs/user/${userId}`, { params });\n  return response.data.data;\n};\n\nexport const getActivityLogStats = async (params: { startDate?: string; endDate?: string } = {}): Promise<ActivityLogStats> => {\n  const response = await axiosInstance.get('/activity-logs/stats', { params });\n  return response.data.data;\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AAGO,MAAM,kBAAkB,OAAO,SAAgC,CAAC,CAAC;IACtE,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAa,CAAC,GAAG,CAAC,kBAAkB;QAAE;IAAO;IACpE,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAEO,MAAM,sBAAsB,OACjC,QACA,SAAmE,CAAC,CAAC;IAErE,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE;QAAE;IAAO;IACnF,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAEO,MAAM,sBAAsB,OAAO,SAAmD,CAAC,CAAC;IAC7F,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAa,CAAC,GAAG,CAAC,wBAAwB;QAAE;IAAO;IAC1E,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  ChevronsLeftIcon,\r\n  ChevronsRightIcon,\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface PaginationProps {\r\n  page: number;\r\n  totalPages: number;\r\n  setPage: (page: number) => void;\r\n  entriesText?: string;\r\n}\r\n\r\nconst pagination = ({\r\n  page,\r\n  totalPages,\r\n  setPage,\r\n  entriesText,\r\n}: PaginationProps) => {\r\n  return (\r\n    <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 px-4 py-2\">\r\n      {entriesText && (\r\n        <div className=\"text-sm text-muted-foreground\">{entriesText}</div>\r\n      )}\r\n\r\n      <div className=\"flex items-center gap-2\">\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(1)}\r\n          disabled={page === 1 || page > totalPages}\r\n        >\r\n          <ChevronsLeftIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(page - 1)}\r\n          disabled={page === 1 || page > totalPages}\r\n        >\r\n          <ChevronLeftIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n        <span className=\"text-sm\">\r\n          Page {page} of {totalPages}\r\n        </span>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(page + 1)}\r\n          disabled={page === totalPages || page > totalPages}\r\n        >\r\n          <ChevronRightIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(totalPages)}\r\n          disabled={page === totalPages || page > totalPages}\r\n        >\r\n          <ChevronsRightIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default pagination;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAMA;AARA;;;;AAiBA,MAAM,aAAa,CAAC,EAClB,IAAI,EACJ,UAAU,EACV,OAAO,EACP,WAAW,EACK;IAChB,qBACE,6LAAC;QAAI,WAAU;;YACZ,6BACC,6LAAC;gBAAI,WAAU;0BAAiC;;;;;;0BAGlD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ;wBACvB,UAAU,SAAS,KAAK,OAAO;kCAE/B,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;kCAE9B,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ,OAAO;wBAC9B,UAAU,SAAS,KAAK,OAAO;kCAE/B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;kCAE7B,6LAAC;wBAAK,WAAU;;4BAAU;4BAClB;4BAAK;4BAAK;;;;;;;kCAElB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ,OAAO;wBAC9B,UAAU,SAAS,cAAc,OAAO;kCAExC,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;kCAE9B,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ;wBACvB,UAAU,SAAS,cAAc,OAAO;kCAExC,cAAA,6LAAC,+NAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvC;uCAEe", "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app/%28dashboard%29/activity-logs/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { ColumnDef } from \"@tanstack/react-table\";\nimport { DataTable } from \"@/app-components/dataTable\";\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { format } from 'date-fns';\nimport { getActivityLogs } from '@/services/activityLogApi';\nimport { toast } from 'sonner';\nimport Pagination from \"@/app-components/pagination\";\nimport { Input } from '@/components/ui/input';\nimport { ActivityLog } from '@/lib/types';\n\nconst PAGE_SIZE = 10;\n\nconst ActivityLogsPage = () => {\n  const [isLoading, setIsLoading] = useState(true);\n  const [logs, setLogs] = useState<ActivityLog[]>([]);\n  const [userTypeFilter, setUserTypeFilter] = useState<\"ALL\" | \"STUDENT\" | \"CLASS\">(\"ALL\");\n  const [activityTypeFilter, setActivityTypeFilter] = useState<\"ALL\" | \"LOGIN\" | \"LOGOUT\" | \"REGISTRATION\">(\"ALL\");\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  const fetchActivityLogs = useCallback(\n    async (page: number, userType?: \"STUDENT\" | \"CLASS\", activityType?: \"LOGIN\" | \"LOGOUT\" | \"REGISTRATION\") => {\n      try {\n        setIsLoading(true);\n        const params = {\n          page,\n          limit: PAGE_SIZE,\n          ...(userType && { userType }),\n          ...(activityType && { activityType }),\n        };\n\n        const response = await getActivityLogs(params);\n        setLogs(response.logs);\n        setTotalPages(response.pagination.totalPages || 1);\n      } catch (error: any) {\n        toast.error(error.message || \"Failed to fetch activity logs\");\n        setLogs([]);\n        setTotalPages(1);\n      } finally {\n        setIsLoading(false);\n      }\n    },\n    []\n  );\n\n  useEffect(() => {\n    fetchActivityLogs(1);\n  }, [fetchActivityLogs]);\n\n  const handleFilter = () => {\n    const userType = userTypeFilter !== \"ALL\" ? userTypeFilter : undefined;\n    const activityType = activityTypeFilter !== \"ALL\" ? activityTypeFilter : undefined;\n    setCurrentPage(1);\n    fetchActivityLogs(1, userType, activityType);\n  };\n\n  const getActivityBadgeColor = (activity: string) => {\n    switch (activity) {\n      case 'LOGIN':\n        return 'bg-green-100 text-green-800 border-green-200';\n      case 'LOGOUT':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'REGISTRATION':\n        return 'bg-blue-100 text-blue-800 border-blue-200';\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getUserTypeBadgeColor = (userType: string) => {\n    switch (userType) {\n      case 'STUDENT':\n        return 'bg-purple-100 text-purple-800 border-purple-200';\n      case 'CLASS':\n        return 'bg-orange-100 text-orange-800 border-orange-200';\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const columns: ColumnDef<ActivityLog>[] = [\n    {\n      accessorKey: \"userDetails.firstName\",\n      header: \"User Name\",\n      cell: ({ row }) => {\n        const userDetails = row.original.userDetails;\n        return userDetails ? `${userDetails.firstName} ${userDetails.lastName}` : \"Unknown User\";\n      },\n    },\n    {\n      accessorKey: \"userType\",\n      header: \"User Type\",\n      cell: ({ row }) => (\n        <Badge className={getUserTypeBadgeColor(row.original.userType)}>\n          {row.original.userType}\n        </Badge>\n      ),\n    },\n    {\n      accessorKey: \"activityType\",\n      header: \"Activity\",\n      cell: ({ row }) => (\n        <Badge className={getActivityBadgeColor(row.original.activityType)}>\n          {row.original.activityType}\n        </Badge>\n      ),\n    },\n    {\n      accessorKey: \"userDetails.email\",\n      header: \"Email\",\n      cell: ({ row }) => row.original.userDetails?.email || \"N/A\",\n    },\n    {\n      accessorKey: \"userDetails.contact\",\n      header: \"Contact\",\n      cell: ({ row }) => {\n        const userDetails = row.original.userDetails;\n        if (!userDetails) return \"N/A\";\n        return userDetails.contact || userDetails.contactNo || \"N/A\";\n      },\n    },\n    {\n      accessorKey: \"createdAt\",\n      header: \"Date & Time\",\n      cell: ({ row }) => format(new Date(row.original.createdAt), 'PPp'),\n    },\n  ];\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"flex justify-between items-center mx-2 mb-2\">\n        <h1 className=\"text-2xl font-bold ms-2\">User Activity Logs</h1>\n        <div className=\"flex gap-4\">\n          <Input\n            placeholder=\"Search by name, email, class...\"\n            className=\"w-2/4\"\n            onKeyDown={(e) => e.key === \"Enter\" && handleFilter()}\n          />\n\n          <Select\n            value={userTypeFilter}\n            onValueChange={(value: \"ALL\" | \"STUDENT\" | \"CLASS\") => setUserTypeFilter(value)}\n          >\n            <SelectTrigger className=\"w-[150px]\">\n              <SelectValue placeholder=\"Filter by user type\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"ALL\">All Types</SelectItem>\n              <SelectItem value=\"STUDENT\">Students</SelectItem>\n              <SelectItem value=\"CLASS\">Classes</SelectItem>\n            </SelectContent>\n          </Select>\n\n          <Select\n            value={activityTypeFilter}\n            onValueChange={(value: \"ALL\" | \"LOGIN\" | \"LOGOUT\" | \"REGISTRATION\") => setActivityTypeFilter(value)}\n          >\n            <SelectTrigger className=\"w-[180px]\">\n              <SelectValue placeholder=\"Filter by activity\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"ALL\">All Activities</SelectItem>\n              <SelectItem value=\"LOGIN\">Login</SelectItem>\n              <SelectItem value=\"LOGOUT\">Logout</SelectItem>\n              <SelectItem value=\"REGISTRATION\">Registration</SelectItem>\n            </SelectContent>\n          </Select>\n\n\n          <div className=\"mt-1 flex items-center space-x-5 me-3\">\n            <Button onClick={handleFilter}>Filter</Button>\n          </div>\n        </div>\n      </div>\n\n      <DataTable\n        columns={columns}\n        data={logs}\n        isLoading={isLoading}\n      />\n\n      <Pagination\n        page={currentPage}\n        totalPages={totalPages}\n        setPage={(page) => {\n          setCurrentPage(page);\n          const userType = userTypeFilter !== \"ALL\" ? userTypeFilter : undefined;\n          const activityType = activityTypeFilter !== \"ALL\" ? activityTypeFilter : undefined;\n          fetchActivityLogs(page, userType, activityType);\n        }}\n        entriesText={`${logs.length} entries`}\n      />\n    </div>\n  );\n};\n\nexport default ActivityLogsPage;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AAeA,MAAM,YAAY;AAElB,MAAM,mBAAmB;;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAClF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C;IAC1G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAClC,OAAO,MAAc,UAAgC;YACnD,IAAI;gBACF,aAAa;gBACb,MAAM,SAAS;oBACb;oBACA,OAAO;oBACP,GAAI,YAAY;wBAAE;oBAAS,CAAC;oBAC5B,GAAI,gBAAgB;wBAAE;oBAAa,CAAC;gBACtC;gBAEA,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE;gBACvC,QAAQ,SAAS,IAAI;gBACrB,cAAc,SAAS,UAAU,CAAC,UAAU,IAAI;YAClD,EAAE,OAAO,OAAY;gBACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;gBAC7B,QAAQ,EAAE;gBACV,cAAc;YAChB,SAAU;gBACR,aAAa;YACf;QACF;0DACA,EAAE;IAGJ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,kBAAkB;QACpB;qCAAG;QAAC;KAAkB;IAEtB,MAAM,eAAe;QACnB,MAAM,WAAW,mBAAmB,QAAQ,iBAAiB;QAC7D,MAAM,eAAe,uBAAuB,QAAQ,qBAAqB;QACzE,eAAe;QACf,kBAAkB,GAAG,UAAU;IACjC;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,UAAoC;QACxC;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ,CAAC,WAAW;gBAC5C,OAAO,cAAc,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAAG;YAC5E;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAW,sBAAsB,IAAI,QAAQ,CAAC,QAAQ;8BAC1D,IAAI,QAAQ,CAAC,QAAQ;;;;;;QAG5B;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAW,sBAAsB,IAAI,QAAQ,CAAC,YAAY;8BAC9D,IAAI,QAAQ,CAAC,YAAY;;;;;;QAGhC;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,IAAI,QAAQ,CAAC,WAAW,EAAE,SAAS;QACxD;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ,CAAC,WAAW;gBAC5C,IAAI,CAAC,aAAa,OAAO;gBACzB,OAAO,YAAY,OAAO,IAAI,YAAY,SAAS,IAAI;YACzD;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,QAAQ,CAAC,SAAS,GAAG;QAC9D;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,WAAU;gCACV,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;0CAGzC,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,eAAe,CAAC,QAAuC,kBAAkB;;kDAEzE,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;0DAC5B,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAI9B,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,eAAe,CAAC,QAAuD,sBAAsB;;kDAE7F,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;0DAC1B,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;0DAC3B,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAe;;;;;;;;;;;;;;;;;;0CAKrC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAKrC,6LAAC,yIAAA,CAAA,YAAS;gBACR,SAAS;gBACT,MAAM;gBACN,WAAW;;;;;;0BAGb,6LAAC,0IAAA,CAAA,UAAU;gBACT,MAAM;gBACN,YAAY;gBACZ,SAAS,CAAC;oBACR,eAAe;oBACf,MAAM,WAAW,mBAAmB,QAAQ,iBAAiB;oBAC7D,MAAM,eAAe,uBAAuB,QAAQ,qBAAqB;oBACzE,kBAAkB,MAAM,UAAU;gBACpC;gBACA,aAAa,GAAG,KAAK,MAAM,CAAC,QAAQ,CAAC;;;;;;;;;;;;AAI7C;GAtLM;KAAA;uCAwLS", "debugId": null}}]}