import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import { 
  getActivityLogs, 
  getUserActivityLogs, 
  getActivityLogStatistics 
} from '../services/activityLogService';

export const getAllActivityLogs = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const userType = req.query.userType as string;
    const activityType = req.query.activityType as string;
    const search = req.query.search as string;
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;

    const result = await getActivityLogs({
      page,
      limit,
      userType,
      activityType,
      search,
      startDate,
      endDate,
    });

    return sendSuccess(res, result, 'Activity logs retrieved successfully');
  } catch (error: any) {
    console.error('Get activity logs error:', error);
    return sendError(res, `Failed to retrieve activity logs: ${error.message}`, 500);
  }
};

export const getActivityLogsByUser = async (req: Request, res: Response): Promise<any> => {
  try {
    const { userId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const activityType = req.query.activityType as string;

    if (!userId) {
      return sendError(res, 'User ID is required', 400);
    }

    const result = await getUserActivityLogs({
      userId,
      page,
      limit,
      activityType,
    });

    return sendSuccess(res, result, 'User activity logs retrieved successfully');
  } catch (error: any) {
    console.error('Get user activity logs error:', error);
    return sendError(res, `Failed to retrieve user activity logs: ${error.message}`, 500);
  }
};

export const getActivityLogStats = async (req: Request, res: Response): Promise<any> => {
  try {
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;

    const stats = await getActivityLogStatistics({
      startDate,
      endDate,
    });

    return sendSuccess(res, stats, 'Activity log statistics retrieved successfully');
  } catch (error: any) {
    console.error('Get activity log stats error:', error);
    return sendError(res, `Failed to retrieve activity log statistics: ${error.message}`, 500);
  }
};
