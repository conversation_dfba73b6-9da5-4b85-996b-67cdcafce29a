'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, RefreshCw, User, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { getActivityLogs, getActivityLogStats, type ActivityLog, type ActivityLogStats } from '@/services/activityLogApi';
import { toast } from 'sonner';

export default function ActivityLogsPage() {
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [stats, setStats] = useState<ActivityLogStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  // Filters
  const [search, setSearch] = useState('');
  const [userType, setUserType] = useState<string>('');
  const [activityType, setActivityType] = useState<string>('');

  const fetchActivityLogs = async (page = 1) => {
    try {
      setLoading(true);
      const params = {
        page,
        limit: pagination.limit,
        ...(search && { search }),
        ...(userType && { userType }),
        ...(activityType && { activityType }),
      };

      const response = await getActivityLogs(params);
      setLogs(response.logs);
      setPagination(response.pagination);
    } catch (error) {
      console.error('Failed to fetch activity logs:', error);
      toast.error('Failed to fetch activity logs');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await getActivityLogStats();
      setStats(response);
    } catch (error) {
      console.error('Failed to fetch activity stats:', error);
      toast.error('Failed to fetch activity statistics');
    }
  };

  useEffect(() => {
    fetchActivityLogs();
    fetchStats();
  }, [userType, activityType]);

  const handleSearch = () => {
    fetchActivityLogs(1);
  };

  const handleReset = () => {
    setSearch('');
    setUserType('');
    setActivityType('');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const getActivityBadgeColor = (activity: string) => {
    switch (activity) {
      case 'LOGIN':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'LOGOUT':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'REGISTRATION':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getUserTypeBadgeColor = (userType: string) => {
    switch (userType) {
      case 'STUDENT':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'CLASS':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">User Activity Logs</h1>
        <Button onClick={() => fetchActivityLogs(pagination.page)} disabled={loading}>
          <RefreshCw className={cn('h-4 w-4 mr-2', loading && 'animate-spin')} />
          Refresh
        </Button>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{stats.overview.totalLogs}</div>
              <p className="text-sm text-muted-foreground">Total Logs</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.overview.loginCount}</div>
              <p className="text-sm text-muted-foreground">Logins</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{stats.overview.logoutCount}</div>
              <p className="text-sm text-muted-foreground">Logouts</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{stats.overview.registrationCount}</div>
              <p className="text-sm text-muted-foreground">Registrations</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{stats.overview.studentLogs}</div>
              <p className="text-sm text-muted-foreground">Student Activities</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">{stats.overview.classLogs}</div>
              <p className="text-sm text-muted-foreground">Class Activities</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="lg:col-span-2">
              <Input
                placeholder="Search by name, email, or contact..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>

            <Select value={userType} onValueChange={setUserType}>
              <SelectTrigger>
                <SelectValue placeholder="User Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Types</SelectItem>
                <SelectItem value="STUDENT">Students</SelectItem>
                <SelectItem value="CLASS">Classes</SelectItem>
              </SelectContent>
            </Select>

            <Select value={activityType} onValueChange={setActivityType}>
              <SelectTrigger>
                <SelectValue placeholder="Activity Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Activities</SelectItem>
                <SelectItem value="LOGIN">Login</SelectItem>
                <SelectItem value="LOGOUT">Logout</SelectItem>
                <SelectItem value="REGISTRATION">Registration</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex gap-2 mt-4">
            <Button onClick={handleSearch} disabled={loading}>
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
            <Button variant="outline" onClick={handleReset}>
              Reset Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Activity Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle>Activity Logs ({pagination.total} total)</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin" />
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No activity logs found
            </div>
          ) : (
            <div className="space-y-4">
              {logs.map((log) => (
                <div key={log.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="font-medium">
                          {log.userDetails ? 
                            `${log.userDetails.firstName} ${log.userDetails.lastName}` : 
                            'Unknown User'
                          }
                        </span>
                      </div>
                      <Badge className={getUserTypeBadgeColor(log.userType)}>
                        {log.userType}
                      </Badge>
                      <Badge className={getActivityBadgeColor(log.activityType)}>
                        {log.activityType}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      {format(new Date(log.createdAt), 'PPp')}
                    </div>
                  </div>
                  
                  {log.userDetails && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      {log.userDetails.email && (
                        <div>
                          <span className="font-medium">Email:</span> {log.userDetails.email}
                        </div>
                      )}
                      {(log.userDetails.contact || log.userDetails.contactNo) && (
                        <div>
                          <span className="font-medium">Contact:</span> {log.userDetails.contact || log.userDetails.contactNo}
                        </div>
                      )}
                      {log.userDetails.className && (
                        <div>
                          <span className="font-medium">Class:</span> {log.userDetails.className}
                        </div>
                      )}
                    </div>
                  )}

                </div>
              ))}
            </div>
          )}
          
          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchActivityLogs(pagination.page - 1)}
                  disabled={pagination.page <= 1 || loading}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchActivityLogs(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages || loading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
