'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, RefreshCw, User, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { getActivityLogs, getActivityLogStats, type ActivityLog, type ActivityLogStats } from '@/services/activityLogApi';
import { toast } from 'sonner';

export default function ActivityLogsPage() {
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [stats, setStats] = useState<ActivityLogStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  const [search, setSearch] = useState('');
  const [userType, setUserType] = useState<string>('ALL');
  const [activityType, setActivityType] = useState<string>('ALL');

  const fetchActivityLogs = async (page = 1) => {
    try {
      setLoading(true);
      const params = {
        page,
        limit: pagination.limit,
        ...(search && { search }),
        ...(userType && userType !== 'ALL' && { userType }),
        ...(activityType && activityType !== 'ALL' && { activityType }),
      };

      const response = await getActivityLogs(params);
      setLogs(response.logs);
      setPagination(response.pagination);
    } catch (error) {
      console.error('Failed to fetch activity logs:', error);
      toast.error('Failed to fetch activity logs');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await getActivityLogStats();
      setStats(response);
    } catch (error) {
      console.error('Failed to fetch activity stats:', error);
      toast.error('Failed to fetch activity statistics');
    }
  };

  useEffect(() => {
    fetchActivityLogs();
    fetchStats();
  }, [userType, activityType]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mx-2 mb-2">
        <h1 className="text-2xl font-bold ms-2">User Activity Logs</h1>
        <div className="flex gap-4">
          <Select>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All</SelectItem>
              <SelectItem value="PENDING">Login</SelectItem>
              <SelectItem value="APPROVED">Log out</SelectItem>
              <SelectItem value="REJECTED">Registration</SelectItem>
            </SelectContent>
          </Select>
          <div className="mt-1 flex items-center space-x-5 me-3">
            <Button >Filter</Button>
          </div>
        </div>
      </div>




    </div>
  );
}
