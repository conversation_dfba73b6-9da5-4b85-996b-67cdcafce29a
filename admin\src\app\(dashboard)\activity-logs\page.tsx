'use client';

import { useState, useEffect, useCallback } from 'react';
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/app-components/dataTable";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { format } from 'date-fns';
import { getActivityLogs } from '@/services/activityLogApi';
import { toast } from 'sonner';
import Pagination from "@/app-components/pagination";
import { Input } from '@/components/ui/input';
import { ActivityLog } from '@/lib/types';

const PAGE_SIZE = 10;

const ActivityLogsPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [userTypeFilter, setUserTypeFilter] = useState<"ALL" | "STUDENT" | "CLASS">("ALL");
  const [activityTypeFilter, setActivityTypeFilter] = useState<"ALL" | "LOGIN" | "LOGOUT" | "REGISTRATION">("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [search, setSearch] = useState('');

  const fetchActivityLogs = useCallback(
    async (page: number, userType?: "STUDENT" | "CLASS", activityType?: "LOGIN" | "LOGOUT" | "REGISTRATION", searchTerm?: string) => {
      try {
        setIsLoading(true);
        const params = {
          page,
          limit: PAGE_SIZE,
          ...(userType && { userType }),
          ...(activityType && { activityType }),
          ...(searchTerm && { search: searchTerm }),
        };

        const response = await getActivityLogs(params);
        setLogs(response.logs);
        setTotalPages(response.pagination.totalPages || 1);
      } catch (error: any) {
        toast.error(error.message || "Failed to fetch activity logs");
        setLogs([]);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    fetchActivityLogs(1);
  }, [fetchActivityLogs]);

  const handleFilter = () => {
    const userType = userTypeFilter !== "ALL" ? userTypeFilter : undefined;
    const activityType = activityTypeFilter !== "ALL" ? activityTypeFilter : undefined;
    const searchTerm = search.trim() || undefined;
    setCurrentPage(1);
    fetchActivityLogs(1, userType, activityType, searchTerm);
  };

  const handleReset = () => {
    setSearch('');
    setUserTypeFilter('ALL');
    setActivityTypeFilter('ALL');
    setCurrentPage(1);
    fetchActivityLogs(1);
  };

  const getActivityBadgeColor = (activity: string) => {
    switch (activity) {
      case 'LOGIN':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'LOGOUT':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'REGISTRATION':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getUserTypeBadgeColor = (userType: string) => {
    switch (userType) {
      case 'STUDENT':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'CLASS':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const columns: ColumnDef<ActivityLog>[] = [
    {
      accessorKey: "userDetails.firstName",
      header: "User Name",
      cell: ({ row }) => {
        const userDetails = row.original.userDetails;
        return userDetails ? `${userDetails.firstName} ${userDetails.lastName}` : "Unknown User";
      },
    },
    {
      accessorKey: "userType",
      header: "User Type",
      cell: ({ row }) => (
        <Badge className={getUserTypeBadgeColor(row.original.userType)}>
          {row.original.userType}
        </Badge>
      ),
    },
    {
      accessorKey: "activityType",
      header: "Activity",
      cell: ({ row }) => (
        <Badge className={getActivityBadgeColor(row.original.activityType)}>
          {row.original.activityType}
        </Badge>
      ),
    },
    {
      accessorKey: "userDetails.email",
      header: "Email",
      cell: ({ row }) => row.original.userDetails?.email || "N/A",
    },
    {
      accessorKey: "userDetails.contact",
      header: "Contact",
      cell: ({ row }) => {
        const userDetails = row.original.userDetails;
        if (!userDetails) return "N/A";
        return userDetails.contact || userDetails.contactNo || "N/A";
      },
    },
    {
      accessorKey: "createdAt",
      header: "Date & Time",
      cell: ({ row }) => format(new Date(row.original.createdAt), 'PPp'),
    },
  ];

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mx-2 mb-2">
        <h1 className="text-2xl font-bold ms-2">User Activity Logs</h1>
        <div className="flex gap-4">
          <Input
            placeholder="Search by name, email, class..."
            className="w-2/4"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleFilter()}
          />

          <Select
            value={userTypeFilter}
            onValueChange={(value: "ALL" | "STUDENT" | "CLASS") => setUserTypeFilter(value)}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by user type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Types</SelectItem>
              <SelectItem value="STUDENT">Students</SelectItem>
              <SelectItem value="CLASS">Classes</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={activityTypeFilter}
            onValueChange={(value: "ALL" | "LOGIN" | "LOGOUT" | "REGISTRATION") => setActivityTypeFilter(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by activity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Activities</SelectItem>
              <SelectItem value="LOGIN">Login</SelectItem>
              <SelectItem value="LOGOUT">Logout</SelectItem>
              <SelectItem value="REGISTRATION">Registration</SelectItem>
            </SelectContent>
          </Select>


          <div className="mt-1 flex items-center space-x-5 me-3">
            <Button onClick={handleFilter}>Filter</Button>
          </div>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={logs}
        isLoading={isLoading}
      />

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={(page) => {
          setCurrentPage(page);
          const userType = userTypeFilter !== "ALL" ? userTypeFilter : undefined;
          const activityType = activityTypeFilter !== "ALL" ? activityTypeFilter : undefined;
          const searchTerm = search.trim() || undefined;
          fetchActivityLogs(page, userType, activityType, searchTerm);
        }}
        entriesText={`${logs.length} entries`}
      />
    </div>
  );
};

export default ActivityLogsPage;
