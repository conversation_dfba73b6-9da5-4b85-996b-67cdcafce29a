import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import {
  createStudent,
  findStudentByEmail,
  getAllStudent,
  getAllStudentCount,
  getAllStudentsCounts,
  updateStudent,
  findUserByContactNo,
  createOtp,
  verifyOtp,
  findUserByContactNoForLogin,
  updateStudentContact,
} from '../services/studentAuthServices';
import { sendError, sendSuccess } from '@/utils/response';
import dotenv from 'dotenv';
import axios from 'axios';
import prisma from '@/config/prismaClient';
import { Status } from '@prisma/client';
import { transporter } from "@/utils/email";
import {
  createAdminNotificationTemplate
} from '@/utils/emailTemplates';
import { createNotification, createAdminNotification } from '@/utils/notifications';
import { UserType, NotificationType } from '@prisma/client';
import { getReferralLinkByCode, createReferral } from '../services/referralService';
import { logLogin, logLogout, logRegistration } from '@/utils/activityLogger';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';
const COOKIE_NAME = "student_jwt";

export const continueWithEmail = async (req: Request, res: Response): Promise<any> => {
  try {
    const { email } = req.body;
    const user = await findStudentByEmail(email);
    if (!user) {
      return sendError(res, 'Email not registered. Please sign up.', 404);
    }

    const isOldUser = !user.contact;
    
    if (!isOldUser) {
      // If contactNo exists (isOldUser: false), send OTP directly
      const contactNo = user.contact!;
      const otpRecord = await createOtp(contactNo);

      const response = await axios.get(
        `${process.env.SHREE_TRIPADA_API_URL}?auth_key=${process.env.SHREE_TRIPADA_AUTH_KEY}&mobiles=91${contactNo}&templateid=${process.env.SHREE_TRIPADA_TEMPLATE_ID}&message=Hi ${user.firstName}, Please use the code ${otpRecord.otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!&sender=${process.env.SHREE_TRIPADA_SENDER}&route=4`
      );

      if (response.status !== 200 || response.data.status !== 'SMS Submitted Successfully') {
        return sendError(res, 'Failed to send OTP', 500);
      }

      return sendSuccess(res, {
        isOldUser: false,
        firstName: user.firstName,
        lastName: user.lastName,
        contactNo: user.contact,
        otpSent: true,
      }, 'OTP sent successfully');
    }

    // If no contactNo (isOldUser: true), ask for contact number
    return sendSuccess(res, {
      isOldUser: true,
      firstName: user.firstName,
      lastName: user.lastName,
      contactNo: null,
    }, 'Email check successful');
  } catch (error: any) {
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};
export const registerStudent = async (req: Request, res: Response): Promise<any> => {
  try {
    const { firstName, lastName, contactNo, referralCode } = req.body;

    const classesExists = await prisma.classes.findFirst({
      where: { contactNo },
    });
    if (classesExists) {
      return sendError(res, 'This mobile number is registered as a class. Please use a different number.', 400);
    }

    const existingStudent = await findUserByContactNo(contactNo);
    if (existingStudent) {
      return sendError(res, 'Mobile number already registered. Please login.', 400);
    }

    const otpRecord = await createOtp(contactNo);

    const response = await axios.get(
      `${process.env.SHREE_TRIPADA_API_URL}?auth_key=${process.env.SHREE_TRIPADA_AUTH_KEY}&mobiles=91${contactNo}&templateid=${process.env.SHREE_TRIPADA_TEMPLATE_ID}&message=Hi ${firstName}, Please use the code ${otpRecord.otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!&sender=${process.env.SHREE_TRIPADA_SENDER}&route=4`
    );

    if (response.status !== 200 || response.data.status !== 'SMS Submitted Successfully') {
      console.log(`Shree Tripada API failed: ${JSON.stringify(response.data)}`);
      return sendError(res, 'Failed to send OTP', 500);
    }

    return sendSuccess(res, { firstName, lastName, referralCode }, 'OTP sent successfully');
  } catch (error: any) {
    console.error('Register student error:', error);
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};

export const loginStudent = async (req: Request, res: Response): Promise<any> => {
  try {
    const { contactNo, email } = req.body;

    const classesExists = await prisma.classes.findFirst({
      where: { contactNo },
    });
    if (classesExists) {
      return sendError(res, 'This mobile number is registered as a class. Please use a different number.', 400);
    }

    let firstName = 'User';
    let lastName = '';

    if (email) {
      const user = await findStudentByEmail(email);
      if (!user) {
        return sendError(res, 'Email not registered. Please sign up.', 404);
      }
      firstName = user.firstName;
      lastName = user.lastName;
    } else {
      const user = await findUserByContactNoForLogin(contactNo);
      if (!user) {
        return sendError(res, 'Mobile number not registered. Please sign up.', 404);
      }
      if (!user.isVerified) {
        return sendError(res, 'Please verify your account', 403);
      }
      firstName = user.firstName;
      lastName = user.lastName;
    }

    const otpRecord = await createOtp(contactNo);

    const response = await axios.get(
      `${process.env.SHREE_TRIPADA_API_URL}?auth_key=${process.env.SHREE_TRIPADA_AUTH_KEY}&mobiles=91${contactNo}&templateid=${process.env.SHREE_TRIPADA_TEMPLATE_ID}&message=Hi ${firstName}, Please use the code ${otpRecord.otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!&sender=${process.env.SHREE_TRIPADA_SENDER}&route=4`
    );

    if (response.status !== 200 || response.data.status !== 'SMS Submitted Successfully') {
      return sendError(res, 'Failed to send OTP', 500);
    }

    return sendSuccess(res, {
      contactNo,
      firstName,
      lastName,
    }, 'OTP sent successfully');
  } catch (error: any) {
    console.error('Login student error:', error);
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};

export const verifyOtpController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { contactNo, otp, email, firstName, lastName, referralCode } = req.body;

    const otpRecord = await verifyOtp(contactNo, otp);
    if (!otpRecord) {
      return sendError(res, 'Invalid or expired OTP', 400);
    }

    let user = await findUserByContactNo(contactNo);

    if (user) {
      const token = jwt.sign(
        { id: user.id, contactNo: user.contact, isVerified: user.isVerified },
        JWT_SECRET,
        { expiresIn: '7d' }
      );

      res.cookie(COOKIE_NAME, token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000,
      });

      await logLogin(user.id, UserType.STUDENT);

      return sendSuccess(res, {
        userId: user.id,
        contactNo: user.contact,
        firstName: user.firstName,
        lastName: user.lastName,
        token,
      }, 'Login successful');
    }

    if (email) {
      user = await findStudentByEmail(email);
      if (!user) {
        return sendError(res, 'Email not registered', 404);
      }
      user = await updateStudentContact(email, contactNo);
    } else {
      if (!firstName || !lastName) {
        return sendError(res, 'First name and last name required for registration', 400);
      }

      user = await createStudent(firstName, lastName, contactNo);

      if (referralCode) {
        try {
          const referralLink = await getReferralLinkByCode(referralCode);
          if (referralLink && referralLink.isActive) {
            await createReferral(
              referralLink.id,
              user.id,
              'STUDENT',
              `${firstName} ${lastName}`,
              ''
            );
          }
        } catch (error) {
          console.log('Referral tracking error:', error);
        }
      }

      const adminNotificationHtml = createAdminNotificationTemplate({
        firstName,
        lastName,
        email: email || '',
        contact: contactNo,
      });

      const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: adminEmail,
        subject: 'New Student Registration',
        html: adminNotificationHtml,
      });
    }

    // Create notification for student
    await createNotification({
      userId: user.id,
      userType: UserType.STUDENT,
      type: NotificationType.STUDENT_ACCOUNT_CREATED,
      title: 'Account Created Successfully',
      message: 'Your account has been created successfully. Please verify your email to complete registration.',
      data: { contactNo: user.contact }
    });

    // Create notification for admin about new student registration
    try {
      await createAdminNotification({
        type: NotificationType.ADMIN_NEW_STUDENT_REGISTRATION,
        title: 'New Student Registration',
        message: `A new student ${firstName} ${lastName} has registered with email ${email}.`,
        data: {
          studentId: user.id,
          studentName: `${firstName} ${lastName}`,
          email: email,
          contact: contactNo
        }
      });
      console.log('✅ Admin notification created for student registration:', firstName, lastName);
    } catch (notificationError) {
      console.error('❌ Failed to create admin notification:', notificationError);
    }

    const token = jwt.sign(
      { id: user.id, contactNo: user.contact, isVerified: user.isVerified },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.cookie(COOKIE_NAME, token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000,
    });
    
    if (email) {
      await logLogin(user.id, UserType.STUDENT);
    } else {
      await logRegistration(user.id, UserType.STUDENT);
    }

    return sendSuccess(res, {
      userId: user.id,
      contactNo: user.contact,
      firstName: user.firstName,
      lastName: user.lastName,
      token,
    }, email ? 'Contact number updated and login successful' : 'Registration successful');
  } catch (error: any) {
    console.error('Verify OTP error:', error);
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};

export const resendOtp = async (req: Request, res: Response): Promise<any> => {
  try {
    const { contactNo, firstName } = req.body;

    const recentOtps = await prisma.otpMessage.count({
      where: {
        contactNo,
        createdAt: { gt: new Date(Date.now() - 60 * 60 * 1000) },
      },
    });
    if (recentOtps >= 3) {
      return sendError(res, 'Too many OTP requests, try again later', 429);
    }

    const otpRecord = await createOtp(contactNo);

    const response = await axios.get(
      `${process.env.SHREE_TRIPADA_API_URL}?auth_key=${process.env.SHREE_TRIPADA_AUTH_KEY}&mobiles=91${contactNo}&templateid=${process.env.SHREE_TRIPADA_TEMPLATE_ID}&message=Hi ${firstName || 'User'}, Please use the code ${otpRecord.otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!&sender=${process.env.SHREE_TRIPADA_SENDER}&route=4`
    );

    if (response.status !== 200 || response.data.status !== 'SMS Submitted Successfully') {
      return sendError(res, 'Failed to send OTP', 500);
    }

    return sendSuccess(res, null, 'New OTP sent');
  } catch (error: any) {
    console.error('Resend OTP error:', error);
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};

export const logout = async (req: Request, res: Response): Promise<any> => {
  try {
    const token = req.cookies[COOKIE_NAME];
    if (token) {
      try {
        const decoded = jwt.verify(token, JWT_SECRET) as { id: string };
        await logLogout(decoded.id, UserType.STUDENT);
        console.log('Logout logged for student:', decoded.id);
      } catch (error) {
        console.log('Failed to decode token for logout logging:', error);
      }
    }

    res.clearCookie(COOKIE_NAME, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });

    return sendSuccess(res, null, 'Logged out successfully');
  } catch (error) {
    console.error('Logout error:', error);
    return sendSuccess(res, null, 'Logged out successfully');
  }
};

export const getAllStudentController = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const includeProfile = req.query.includeProfile === 'true';
    const skip = (page - 1) * limit;

    const name = req.query.name as string | undefined;
    const email = req.query.email as string | undefined;
    const contact = req.query.contact as string | undefined;
    const statusParam = req.query.status as string | undefined;

    const validStatuses: Status[] = ['PENDING', 'APPROVED', 'REJECTED'];
    const status = statusParam && validStatuses.includes(statusParam as Status) ? statusParam as Status : undefined;

    const [students, total] = await Promise.all([
      getAllStudent(skip, limit, includeProfile, { name, email, contact, status }),
      prisma.student.count({
        where: {
          ...(name && {
            OR: [
              { firstName: { contains: name, mode: 'insensitive' } },
              { lastName: { contains: name, mode: 'insensitive' } },
            ],
          }),
          ...(email && { email: { contains: email, mode: 'insensitive' } }),
          ...(contact && { contact: { contains: contact, mode: 'insensitive' } }),
          ...(status && {
            profile: {
              status: status,
            },
          }),
        },
      }),
    ]);

    res.status(200).json({
      students,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (error: any) {
    res.status(500).json({ message: 'Failed to get students', error });
  }
};

export const getCurrentStudentController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        contact: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!student) {
      return sendError(res, 'Student not found', 404);
    }

    const coins = await prisma.uestCoins.findFirst({
      where: {
        modelType: 'STUDENT',
        modelId: studentId,
      },
    });

    const studentWithCoins = {
      ...student,
      coins: coins?.coins ?? 0,
    };

    return sendSuccess(res, studentWithCoins, 'Student data retrieved successfully');
  } catch (error) {
    console.error('Error getting current student:', error);
    return sendError(res, 'Failed to get student data', 500);
  }
};

export const updateStudentController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const { firstName, lastName, contact } = req.body;

    if (!firstName && !lastName && !contact) {
      return sendError(res, 'At least one field is required for update', 400);
    }

    const updateData: {
      firstName?: string;
      lastName?: string;
      contact?: string;
    } = {};

    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (contact) updateData.contact = contact;

    const updatedStudent = await updateStudent(studentId, updateData);

    return sendSuccess(res, updatedStudent, 'Student data updated successfully');
  } catch (error) {
    console.error('Error updating student:', error);
    return sendError(res, 'Failed to update student data', 500);
  }
};

export const getCountStudent = async (_req: Request, res: Response) => {
  try {
    const total = await getAllStudentCount();
    res.status(200).json(total);
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: 'Failed to get students count' });
  }
};

export const getAllStudentCountsController = async (_req: Request, res: Response) => {
  try {
    const counts = await getAllStudentsCounts();
    res.status(200).json(counts);
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: 'Failed to get student counts' });
  }
};