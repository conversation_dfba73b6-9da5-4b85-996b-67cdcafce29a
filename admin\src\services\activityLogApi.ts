import axiosInstance from '@/lib/axios';
import { ActivityLogResponse, ActivityLogStats, GetActivityLogsParams } from '@/lib/types';

export const getActivityLogs = async (params: GetActivityLogsParams = {}): Promise<ActivityLogResponse> => {
  const response = await axiosInstance.get('/activity-logs', { params });
  return response.data.data;
};

export const getUserActivityLogs = async (
  userId: string, 
  params: { page?: number; limit?: number; activityType?: string } = {}
): Promise<ActivityLogResponse> => {
  const response = await axiosInstance.get(`/activity-logs/user/${userId}`, { params });
  return response.data.data;
};

export const getActivityLogStats = async (params: { startDate?: string; endDate?: string } = {}): Promise<ActivityLogStats> => {
  const response = await axiosInstance.get('/activity-logs/stats', { params });
  return response.data.data;
};
