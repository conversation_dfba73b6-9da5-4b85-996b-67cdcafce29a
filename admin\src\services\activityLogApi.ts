import axiosInstance from '@/lib/axios';

export interface ActivityLog {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS';
  activityType: 'LOGIN' | 'LOGOUT' | 'REGISTRATION';
  createdAt: string;
  userDetails?: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    contact?: string;
    contactNo?: string;
    className?: string;
    profilePhoto?: string;
  };
}

export interface ActivityLogResponse {
  logs: ActivityLog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ActivityLogStats {
  overview: {
    totalLogs: number;
    loginCount: number;
    logoutCount: number;
    registrationCount: number;
    studentLogs: number;
    classLogs: number;
  };
  dailyActivity: Array<{
    activityType: string;
    _count: number;
  }>;
}

export interface GetActivityLogsParams {
  page?: number;
  limit?: number;
  userType?: string;
  activityType?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
}

export const getActivityLogs = async (params: GetActivityLogsParams = {}): Promise<ActivityLogResponse> => {
  const response = await axiosInstance.get('/activity-logs', { params });
  return response.data.data;
};

export const getUserActivityLogs = async (
  userId: string, 
  params: { page?: number; limit?: number; activityType?: string } = {}
): Promise<ActivityLogResponse> => {
  const response = await axiosInstance.get(`/activity-logs/user/${userId}`, { params });
  return response.data.data;
};

export const getActivityLogStats = async (params: { startDate?: string; endDate?: string } = {}): Promise<ActivityLogStats> => {
  const response = await axiosInstance.get('/activity-logs/stats', { params });
  return response.data.data;
};
